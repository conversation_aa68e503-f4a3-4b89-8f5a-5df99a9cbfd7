# ATMA Frontend Implementation Guide

## 🚀 Project Setup

### 1. Initialize Project
```bash
# Create Vite React TypeScript project
npm create vite@latest atma-frontend -- --template react-ts
cd atma-frontend
npm install

# Install core dependencies
npm install @tanstack/react-query zustand react-router-dom react-hook-form @hookform/resolvers zod axios socket.io-client

# Install UI dependencies
npm install tailwindcss postcss autoprefixer @tailwindcss/forms
npm install lucide-react class-variance-authority clsx tailwind-merge

# Install shadcn/ui
npx shadcn-ui@latest init
npx shadcn-ui@latest add button input card form dialog progress badge toast tabs slider
```

### 2. Project Structure
```
src/
├── components/
│   ├── ui/                 # shadcn components
│   ├── forms/
│   │   ├── AssessmentForm.tsx
│   │   ├── LoginForm.tsx
│   │   └── RegisterForm.tsx
│   ├── layout/
│   │   ├── AppLayout.tsx
│   │   ├── AuthLayout.tsx
│   │   └── ProtectedRoute.tsx
│   └── features/
│       ├── assessment/
│       ├── auth/
│       └── dashboard/
├── pages/
│   ├── auth/
│   ├── assessment/
│   └── dashboard/
├── hooks/
├── stores/
├── services/
├── types/
├── utils/
└── lib/
```

## 🗃️ State Management

### 1. Zustand Stores

#### Auth Store (`stores/authStore.ts`)
```typescript
interface User {
  id: string;
  email: string;
  user_type: string;
  token_balance: number;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (email: string, password: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}
```

#### Assessment Store (`stores/assessmentStore.ts`)
```typescript
interface AssessmentData {
  riasec: Record<string, number>;
  ocean: Record<string, number>;
  viaIs: Record<string, number>;
}

interface AssessmentState {
  currentStep: number;
  assessmentData: AssessmentData;
  isSubmitting: boolean;
  jobId: string | null;
  updateAssessmentData: (step: string, data: Record<string, number>) => void;
  submitAssessment: () => Promise<void>;
  resetAssessment: () => void;
}
```

#### UI Store (`stores/uiStore.ts`)
```typescript
interface UIState {
  isLoading: boolean;
  notifications: Notification[];
  modals: Record<string, boolean>;
  setLoading: (loading: boolean) => void;
  addNotification: (notification: Notification) => void;
  toggleModal: (modalId: string) => void;
}
```

### 2. TanStack Query Setup (`lib/queryClient.ts`)
```typescript
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});
```

## 🌐 API Services

### 1. Base API Client (`services/api.ts`)
```typescript
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle token expiration
      localStorage.removeItem('auth_token');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);
```

### 2. Auth Service (`services/authService.ts`)
```typescript
interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  email: string;
  password: string;
}

export const authService = {
  login: async (data: LoginRequest) => {
    const response = await apiClient.post('/api/auth/login', data);
    return response.data;
  },

  register: async (data: RegisterRequest) => {
    const response = await apiClient.post('/api/auth/register', data);
    return response.data;
  },

  getProfile: async () => {
    const response = await apiClient.get('/api/auth/profile');
    return response.data;
  },

  updateProfile: async (data: any) => {
    const response = await apiClient.put('/api/auth/profile', data);
    return response.data;
  },

  logout: async () => {
    await apiClient.post('/api/auth/logout');
  },

  getTokenBalance: async () => {
    const response = await apiClient.get('/api/auth/token-balance');
    return response.data;
  },
};
```

### 3. Assessment Service (`services/assessmentService.ts`)
```typescript
interface AssessmentSubmission {
  assessmentName: string;
  riasec: Record<string, number>;
  ocean: Record<string, number>;
  viaIs: Record<string, number>;
}

export const assessmentService = {
  submitAssessment: async (data: AssessmentSubmission) => {
    const response = await apiClient.post('/api/assessment/submit', data, {
      headers: {
        'X-Idempotency-Key': `assessment-${Date.now()}-${Math.random()}`,
      },
    });
    return response.data;
  },

  getAssessmentStatus: async (jobId: string) => {
    const response = await apiClient.get(`/api/assessment/status/${jobId}`);
    return response.data;
  },

  getQueueStatus: async () => {
    const response = await apiClient.get('/api/assessment/queue/status');
    return response.data;
  },
};
```

### 4. Archive Service (`services/archiveService.ts`)
```typescript
export const archiveService = {
  getResults: async (params?: { page?: number; limit?: number; status?: string }) => {
    const response = await apiClient.get('/api/archive/results', { params });
    return response.data;
  },

  getResultById: async (id: string) => {
    const response = await apiClient.get(`/api/archive/results/${id}`);
    return response.data;
  },

  getUserStats: async () => {
    const response = await apiClient.get('/api/archive/stats');
    return response.data;
  },

  getUserOverview: async () => {
    const response = await apiClient.get('/api/archive/stats/overview');
    return response.data;
  },
};
```

## 🔌 WebSocket Integration

### WebSocket Service (`services/websocketService.ts`)
```typescript
import { io, Socket } from 'socket.io-client';

class WebSocketService {
  private socket: Socket | null = null;
  private token: string | null = null;

  connect(token: string) {
    this.token = token;
    this.socket = io('http://localhost:3000', {
      autoConnect: false,
      transports: ['websocket', 'polling'],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.socket.on('connect', () => {
      this.socket?.emit('authenticate', { token });
    });

    this.socket.on('authenticated', (data) => {
      console.log('WebSocket authenticated:', data.email);
    });

    this.socket.connect();
  }

  onAnalysisStarted(callback: (data: any) => void) {
    this.socket?.on('analysis-started', callback);
  }

  onAnalysisComplete(callback: (data: any) => void) {
    this.socket?.on('analysis-complete', callback);
  }

  onAnalysisFailed(callback: (data: any) => void) {
    this.socket?.on('analysis-failed', callback);
  }

  disconnect() {
    this.socket?.disconnect();
    this.socket = null;
  }
}

export const websocketService = new WebSocketService();
```

## 📝 Form Components

### 1. Assessment Form Structure
```typescript
// Multi-step form with validation
const ASSESSMENT_STEPS = [
  { id: 'riasec', title: 'RIASEC Assessment', fields: 6 },
  { id: 'ocean', title: 'Big Five Personality', fields: 5 },
  { id: 'viaIs', title: 'Character Strengths', fields: 24 },
  { id: 'review', title: 'Review & Submit', fields: 0 },
];

// Validation schemas
const riasecSchema = z.object({
  realistic: z.number().min(0).max(100),
  investigative: z.number().min(0).max(100),
  artistic: z.number().min(0).max(100),
  social: z.number().min(0).max(100),
  enterprising: z.number().min(0).max(100),
  conventional: z.number().min(0).max(100),
});
```

### 2. Form Auto-save Hook
```typescript
const useAutoSave = (data: any, key: string) => {
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      localStorage.setItem(key, JSON.stringify(data));
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [data, key]);
};
```

## 🎨 UI Components

### 1. Layout Components
- `AppLayout`: Main navigation, user menu, token balance
- `AuthLayout`: Centered layout for login/register
- `ProtectedRoute`: Authentication guard wrapper

### 2. Feature Components
- `AssessmentWizard`: Multi-step form container
- `AssessmentMonitor`: Real-time status display
- `ResultsChart`: Data visualization
- `UserDashboard`: Overview cards and stats

### 3. Custom Hooks
- `useAuth`: Authentication state and actions
- `useAssessment`: Assessment form state
- `useWebSocket`: WebSocket connection management
- `useLocalStorage`: Persistent local storage

## 🔒 Security Implementation

### 1. Token Management
```typescript
// Secure token storage
const TOKEN_KEY = 'atma_auth_token';

export const tokenManager = {
  setToken: (token: string) => {
    localStorage.setItem(TOKEN_KEY, token);
  },
  
  getToken: () => {
    return localStorage.getItem(TOKEN_KEY);
  },
  
  removeToken: () => {
    localStorage.removeItem(TOKEN_KEY);
  },
  
  isTokenValid: (token: string) => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp > Date.now() / 1000;
    } catch {
      return false;
    }
  },
};
```

### 2. Input Validation
- Zod schemas for all forms
- Client-side validation with server-side backup
- Sanitization for user inputs
- Rate limiting awareness

## 📱 Responsive Design

### 1. Breakpoint Strategy
```css
/* Tailwind breakpoints */
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet */
lg: 1024px  /* Desktop */
xl: 1280px  /* Large desktop */
```

### 2. Mobile-First Components
- Touch-friendly form controls
- Optimized navigation for mobile
- Responsive charts and tables
- Progressive disclosure for complex forms

## 🧪 Testing Strategy

### 1. Unit Tests
- Component testing with React Testing Library
- Hook testing with @testing-library/react-hooks
- Service layer testing with MSW

### 2. Integration Tests
- Form submission flows
- Authentication flows
- WebSocket connection testing

### 3. E2E Tests (Optional)
- Critical user journeys
- Assessment submission flow
- Results viewing flow

## 🚀 Deployment

### 1. Environment Configuration
```env
VITE_API_BASE_URL=http://localhost:3000
VITE_WS_URL=http://localhost:3000
VITE_APP_NAME=ATMA Frontend
```

### 2. Build Optimization
- Code splitting by routes
- Bundle analysis
- Asset optimization
- Service worker for caching

## 🎯 Component Implementation Details

### 1. Assessment Form Components

#### RIASEC Assessment Component
```typescript
const RiasecAssessment = () => {
  const { control, watch } = useFormContext();

  const riasecFields = [
    { key: 'realistic', label: 'Realistic', description: 'Hands-on, practical work' },
    { key: 'investigative', label: 'Investigative', description: 'Research and analysis' },
    { key: 'artistic', label: 'Artistic', description: 'Creative and expressive' },
    { key: 'social', label: 'Social', description: 'Helping and teaching others' },
    { key: 'enterprising', label: 'Enterprising', description: 'Leadership and business' },
    { key: 'conventional', label: 'Conventional', description: 'Organized and detail-oriented' },
  ];

  return (
    <div className="space-y-6">
      {riasecFields.map((field) => (
        <div key={field.key} className="space-y-2">
          <Label>{field.label}</Label>
          <p className="text-sm text-muted-foreground">{field.description}</p>
          <Controller
            name={`riasec.${field.key}`}
            control={control}
            render={({ field: formField }) => (
              <Slider
                value={[formField.value || 0]}
                onValueChange={(value) => formField.onChange(value[0])}
                max={100}
                step={1}
                className="w-full"
              />
            )}
          />
          <div className="text-right text-sm text-muted-foreground">
            {watch(`riasec.${field.key}`) || 0}/100
          </div>
        </div>
      ))}
    </div>
  );
};
```

#### Assessment Progress Component
```typescript
const AssessmentProgress = ({ currentStep, totalSteps }: { currentStep: number; totalSteps: number }) => {
  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="w-full space-y-2">
      <div className="flex justify-between text-sm">
        <span>Step {currentStep} of {totalSteps}</span>
        <span>{Math.round(progress)}% Complete</span>
      </div>
      <Progress value={progress} className="w-full" />
    </div>
  );
};
```

### 2. Real-time Monitoring Components

#### Assessment Status Monitor
```typescript
const AssessmentMonitor = ({ jobId }: { jobId: string }) => {
  const [status, setStatus] = useState('queued');
  const [queuePosition, setQueuePosition] = useState(0);

  useEffect(() => {
    const token = tokenManager.getToken();
    if (token) {
      websocketService.connect(token);

      websocketService.onAnalysisStarted((data) => {
        if (data.jobId === jobId) {
          setStatus('processing');
        }
      });

      websocketService.onAnalysisComplete((data) => {
        if (data.jobId === jobId) {
          setStatus('completed');
          // Redirect to results
        }
      });

      websocketService.onAnalysisFailed((data) => {
        if (data.jobId === jobId) {
          setStatus('failed');
        }
      });
    }

    return () => websocketService.disconnect();
  }, [jobId]);

  const getStatusIcon = () => {
    switch (status) {
      case 'queued': return <Clock className="h-4 w-4" />;
      case 'processing': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center space-x-2 mb-4">
        {getStatusIcon()}
        <h3 className="text-lg font-semibold">Assessment Status</h3>
      </div>

      <div className="space-y-4">
        <div>
          <p className="text-sm text-muted-foreground">Current Status</p>
          <p className="font-medium capitalize">{status}</p>
        </div>

        {status === 'queued' && (
          <div>
            <p className="text-sm text-muted-foreground">Queue Position</p>
            <p className="font-medium">{queuePosition}</p>
          </div>
        )}

        {status === 'processing' && (
          <div>
            <p className="text-sm text-muted-foreground">Estimated Time</p>
            <p className="font-medium">2-5 minutes</p>
          </div>
        )}
      </div>
    </Card>
  );
};
```

### 3. Results Visualization Components

#### Results Chart Component
```typescript
import { ResponsiveRadar } from '@nivo/radar';

const ResultsChart = ({ data, type }: { data: any; type: 'riasec' | 'ocean' | 'viaIs' }) => {
  const formatData = () => {
    switch (type) {
      case 'riasec':
        return [
          { trait: 'Realistic', value: data.realistic },
          { trait: 'Investigative', value: data.investigative },
          { trait: 'Artistic', value: data.artistic },
          { trait: 'Social', value: data.social },
          { trait: 'Enterprising', value: data.enterprising },
          { trait: 'Conventional', value: data.conventional },
        ];
      case 'ocean':
        return [
          { trait: 'Openness', value: data.openness },
          { trait: 'Conscientiousness', value: data.conscientiousness },
          { trait: 'Extraversion', value: data.extraversion },
          { trait: 'Agreeableness', value: data.agreeableness },
          { trait: 'Neuroticism', value: data.neuroticism },
        ];
      default:
        return [];
    }
  };

  return (
    <div className="h-96">
      <ResponsiveRadar
        data={formatData()}
        keys={['value']}
        indexBy="trait"
        maxValue={100}
        margin={{ top: 40, right: 80, bottom: 40, left: 80 }}
        curve="linearClosed"
        borderWidth={2}
        borderColor={{ from: 'color' }}
        gridLevels={5}
        gridShape="circular"
        gridLabelOffset={36}
        enableDots={true}
        dotSize={10}
        dotColor={{ theme: 'background' }}
        dotBorderWidth={2}
        dotBorderColor={{ from: 'color' }}
        enableDotLabel={true}
        dotLabel="value"
        dotLabelYOffset={-12}
        colors={{ scheme: 'nivo' }}
        fillOpacity={0.25}
        blendMode="multiply"
        animate={true}
        motionConfig="wobbly"
      />
    </div>
  );
};
```

### 4. Custom Hooks Implementation

#### useAssessmentForm Hook
```typescript
const useAssessmentForm = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addNotification } = useUIStore();

  const form = useForm({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      riasec: {},
      ocean: {},
      viaIs: {},
    },
  });

  const nextStep = async () => {
    const isValid = await form.trigger();
    if (isValid && currentStep < ASSESSMENT_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const submitAssessment = async (data: AssessmentData) => {
    setIsSubmitting(true);
    try {
      const result = await assessmentService.submitAssessment({
        assessmentName: 'AI-Driven Talent Mapping',
        ...data,
      });

      addNotification({
        type: 'success',
        message: 'Assessment submitted successfully!',
      });

      return result.data.jobId;
    } catch (error) {
      addNotification({
        type: 'error',
        message: 'Failed to submit assessment. Please try again.',
      });
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    currentStep,
    isSubmitting,
    nextStep,
    prevStep,
    submitAssessment,
  };
};
```

#### useTokenBalance Hook
```typescript
const useTokenBalance = () => {
  const { data: tokenBalance, refetch } = useQuery({
    queryKey: ['tokenBalance'],
    queryFn: authService.getTokenBalance,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const hasEnoughTokens = (required: number = 1) => {
    return (tokenBalance?.data?.token_balance || 0) >= required;
  };

  return {
    tokenBalance: tokenBalance?.data?.token_balance || 0,
    hasEnoughTokens,
    refetchBalance: refetch,
  };
};
```

## 🎨 Styling and Theme

### 1. Tailwind Configuration
```javascript
// tailwind.config.js
module.exports = {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
};
```

### 2. CSS Variables for Theme
```css
/* globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

## 🔧 Error Handling & Loading States

### 1. Error Boundary Component
```typescript
class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: any) {
    return { hasError: true, error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <Card className="p-6 max-w-md">
            <h2 className="text-lg font-semibold mb-2">Something went wrong</h2>
            <p className="text-muted-foreground mb-4">
              We're sorry, but something unexpected happened. Please refresh the page and try again.
            </p>
            <Button onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### 2. Loading States
```typescript
const LoadingSpinner = ({ size = 'default' }: { size?: 'sm' | 'default' | 'lg' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    default: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]}`} />
  );
};

const LoadingSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-3/4" />
    <Skeleton className="h-4 w-1/2" />
  </div>
);
```

## 📱 Responsive Design Patterns

### 1. Mobile Navigation
```typescript
const MobileNav = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="md:hidden">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Menu className="h-5 w-5" />
      </Button>

      {isOpen && (
        <div className="absolute top-16 left-0 right-0 bg-background border-b shadow-lg">
          <nav className="p-4 space-y-2">
            <Link to="/dashboard" className="block py-2">Dashboard</Link>
            <Link to="/assessment/new" className="block py-2">New Assessment</Link>
            <Link to="/results" className="block py-2">Results</Link>
          </nav>
        </div>
      )}
    </div>
  );
};
```

### 2. Responsive Form Layout
```typescript
const ResponsiveFormLayout = ({ children }: { children: React.ReactNode }) => (
  <div className="container mx-auto px-4 py-8">
    <div className="max-w-2xl mx-auto">
      <Card className="p-6 md:p-8">
        {children}
      </Card>
    </div>
  </div>
);
```

This comprehensive implementation guide provides all the necessary details for building a robust, scalable MVP that handles the core assessment functionality while maintaining excellent user experience and code quality.
