{"name": "atma-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.83.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react-hook-form": "^7.60.0", "react-router-dom": "^7.7.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.0.5", "zustand": "^5.0.6"}}